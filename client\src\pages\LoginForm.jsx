import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import AuthLayout from "../components/Auth/AuthLayout";
import axios from "axios";
import { motion } from "framer-motion";
import GoogleLoginButton from "../components/GoogleLoginButton";

const LoginForm = () => {
  const navigate = useNavigate();
  const [form, setForm] = useState({ email: "", password: "" });
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setLoading(true);

    try {
      const res = await axios.post("http://localhost:8080/api/users/login", form);
      localStorage.setItem("token", res.data.token);
      navigate("/dashboard");
    } catch (err) {
      console.error(err);
      setError(err.response?.data?.message || "Login failed");
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthLayout title="Welcome Back to Procrastinot">
      <motion.div
        className="w-full max-w-md mx-auto"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        {/* Enhanced Header */}
        <motion.div
          className="text-center mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.6 }}
        >
          <motion.div
            className="text-4xl mb-4"
            animate={{
              rotate: [0, 10, -10, 0],
              scale: [1, 1.1, 1]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            👋
          </motion.div>
          <h2 className="text-2xl font-display font-bold text-white mb-2">
            Welcome Back!
          </h2>
          <p className="text-white/70 font-medium">
            Ready to boost your productivity?
          </p>
        </motion.div>

        <motion.form
          onSubmit={handleSubmit}
          className="space-y-6 glass-card p-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            <label className="block mb-3 text-sm font-bold text-white flex items-center gap-2">
              <span className="text-primary-400">📧</span>
              Email Address
            </label>
            <motion.input
              type="email"
              name="email"
              value={form.email}
              onChange={handleChange}
              required
              className="input-glass w-full"
              placeholder="Enter your email address"
              whileFocus={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5, duration: 0.5 }}
          >
            <label className="block mb-3 text-sm font-bold text-white flex items-center gap-2">
              <span className="text-secondary-400">🔒</span>
              Password
            </label>
            <motion.input
              type="password"
              name="password"
              value={form.password}
              onChange={handleChange}
              required
              className="input-glass w-full"
              placeholder="Enter your password"
              whileFocus={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            />
          </motion.div>

          {error && (
            <motion.div
              className="glass-card p-4 border-l-4 border-error-500 bg-error-500/10"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
            >
              <p className="text-error-400 text-sm font-medium flex items-center gap-2">
                <span>⚠️</span>
                {error}
              </p>
            </motion.div>
          )}

          <motion.button
            type="submit"
            disabled={loading}
            className="btn-primary w-full py-4 text-lg font-bold shadow-button-hover"
            whileHover={{ scale: 1.02, y: -2 }}
            whileTap={{ scale: 0.98 }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.5 }}
          >
            <span className="flex items-center justify-center gap-3">
              {loading ? (
                <>
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  >
                    ⏳
                  </motion.div>
                  Signing you in...
                </>
              ) : (
                <>
                  <motion.span
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  >
                    🚀
                  </motion.span>
                  Sign In
                </>
              )}
            </span>
          </motion.button>

          <motion.div
            className="text-center text-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.7, duration: 0.5 }}
          >
            <span className="text-white/70">Don't have an account?</span>{" "}
            <motion.a
              href="/register"
              className="text-gradient-primary font-bold hover-lift inline-block"
              whileHover={{ scale: 1.05 }}
            >
              Create Account
            </motion.a>
          </motion.div>

          <motion.div
            className="flex items-center justify-center gap-4 my-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.5 }}
          >
            <span className="h-px bg-gradient-to-r from-transparent via-white/30 to-transparent flex-1"></span>
            <span className="text-white/60 text-sm font-semibold px-4 glass-card py-2 rounded-full">
              OR
            </span>
            <span className="h-px bg-gradient-to-r from-transparent via-white/30 to-transparent flex-1"></span>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.9, duration: 0.5 }}
          >
            <GoogleLoginButton />
          </motion.div>
        </motion.form>
      </motion.div>
    </AuthLayout>
  );
};

export default LoginForm;
