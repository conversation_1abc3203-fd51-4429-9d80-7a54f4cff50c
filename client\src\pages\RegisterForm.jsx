import React, { useState, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import AuthLayout from "../components/Auth/AuthLayout";
import axios from "axios";
import { motion } from "framer-motion";
import GoogleLoginButton from "../components/GoogleLoginButton";

const RegisterForm = () => {
  const navigate = useNavigate();
  const [form, setForm] = useState({
    username: "",
    email: "",
    password: "",
  });
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);

  const handleChange = useCallback((e) => {
    setForm((prevForm) => ({
      ...prevForm,
      [e.target.name]: e.target.value,
    }));
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setLoading(true);

    try {
      const res = await axios.post("http://localhost:8080/api/users/register", form);
      localStorage.setItem("token", res.data.token);
      navigate("/dashboard");
    } catch (err) {
      console.error(err);
      setError(err.response?.data?.message || "Registration failed");
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthLayout title="Join the Procrastinot Community">
      <motion.div
        className="w-full max-w-md mx-auto"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        {/* Enhanced Header */}
        <motion.div
          className="text-center mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.6 }}
        >
          <motion.div
            className="text-4xl mb-4"
            animate={{
              rotate: [0, 10, -10, 0],
              scale: [1, 1.1, 1]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            🎉
          </motion.div>
          <h2 className="text-2xl font-display font-bold text-white mb-2">
            Start Your Journey!
          </h2>
          <p className="text-white/70 font-medium">
            Join thousands who've conquered procrastination
          </p>
        </motion.div>

        <motion.form
          onSubmit={handleSubmit}
          className="space-y-6 glass-card p-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            <label className="block mb-3 text-sm font-bold text-white flex items-center gap-2">
              <span className="text-accent-400">👤</span>
              Full Name
            </label>
            <motion.input
              type="text"
              name="username"
              value={form.username}
              onChange={handleChange}
              required
              autoComplete="name"
              className="input-glass w-full"
              placeholder="Enter your full name"
              whileFocus={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5, duration: 0.5 }}
          >
            <label className="block mb-3 text-sm font-bold text-white flex items-center gap-2">
              <span className="text-primary-400">📧</span>
              Email Address
            </label>
            <motion.input
              type="email"
              name="email"
              value={form.email}
              onChange={handleChange}
              required
              autoComplete="email"
              className="input-glass w-full"
              placeholder="Enter your email address"
              whileFocus={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.6, duration: 0.5 }}
          >
            <label className="block mb-3 text-sm font-bold text-white flex items-center gap-2">
              <span className="text-secondary-400">🔒</span>
              Password
            </label>
            <motion.input
              type="password"
              name="password"
              value={form.password}
              onChange={handleChange}
              required
              autoComplete="new-password"
              className="input-glass w-full"
              placeholder="Create a strong password"
              whileFocus={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            />
            <p className="text-white/60 text-xs mt-2 font-medium">
              💡 Use at least 8 characters with letters and numbers
            </p>
          </motion.div>

          {error && (
            <motion.div
              className="glass-card p-4 border-l-4 border-error-500 bg-error-500/10"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
            >
              <p className="text-error-400 text-sm font-medium flex items-center gap-2">
                <span>⚠️</span>
                {error}
              </p>
            </motion.div>
          )}

          <motion.button
            type="submit"
            disabled={loading}
            className="btn-primary w-full py-4 text-lg font-bold shadow-button-hover"
            whileHover={{ scale: 1.02, y: -2 }}
            whileTap={{ scale: 0.98 }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7, duration: 0.5 }}
          >
            <span className="flex items-center justify-center gap-3">
              {loading ? (
                <>
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  >
                    ⏳
                  </motion.div>
                  Creating your account...
                </>
              ) : (
                <>
                  <motion.span
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                  >
                    🚀
                  </motion.span>
                  Create Account
                </>
              )}
            </span>
          </motion.button>

          <motion.div
            className="text-center text-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.5 }}
          >
            <span className="text-white/70">Already have an account?</span>{" "}
            <motion.a
              href="/login"
              className="text-gradient-primary font-bold hover-lift inline-block"
              whileHover={{ scale: 1.05 }}
            >
              Sign In
            </motion.a>
          </motion.div>

          <motion.div
            className="flex items-center justify-center gap-4 my-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.9, duration: 0.5 }}
          >
            <span className="h-px bg-gradient-to-r from-transparent via-white/30 to-transparent flex-1"></span>
            <span className="text-white/60 text-sm font-semibold px-4 glass-card py-2 rounded-full">
              OR
            </span>
            <span className="h-px bg-gradient-to-r from-transparent via-white/30 to-transparent flex-1"></span>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.0, duration: 0.5 }}
          >
            <GoogleLoginButton />
          </motion.div>

          {/* Terms and Privacy */}
          <motion.p
            className="text-center text-xs text-white/60 mt-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.1, duration: 0.5 }}
          >
            By creating an account, you agree to our{" "}
            <a href="#" className="text-primary-400 hover:text-primary-300 transition-colors duration-200">
              Terms of Service
            </a>{" "}
            and{" "}
            <a href="#" className="text-primary-400 hover:text-primary-300 transition-colors duration-200">
              Privacy Policy
            </a>
          </motion.p>
        </motion.form>
      </motion.div>
    </AuthLayout>
  );
};

export default RegisterForm;
