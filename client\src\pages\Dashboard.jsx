// src/pages/Dashboard.jsx
import Navbar from '../components/Navbar1';
import Footer from '../components/Footer';
import FeatureCard from '../components/FeatureCard';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  TimerIcon,
  SmileIcon,
  BrainIcon,
  CalendarIcon,
  RocketIcon,
  GraduationCapIcon,
  TrendingUp,
  Clock,
  Target,
  Zap,
  Award,
  User
} from 'lucide-react';
import { useState, useEffect } from 'react';

export default function Dashboard() {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [userName] = useState('Chethan'); // This would come from auth context

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  const features = [
    {
      title: 'Pomodoro Timer',
      description: 'Boost focus with scientifically-proven time management techniques.',
      icon: <TimerIcon className="w-8 h-8" />,
      to: '/pomodoro',
      gradient: 'from-red-500 to-orange-500'
    },
    {
      title: 'Mood Tracker',
      description: 'Monitor your emotional journey and productivity patterns.',
      icon: <SmileIcon className="w-8 h-8" />,
      to: '/mood',
      gradient: 'from-yellow-500 to-pink-500'
    },
    {
      title: 'AI Task Assistant',
      description: 'Get intelligent task breakdowns and smart recommendations.',
      icon: <BrainIcon className="w-8 h-8" />,
      to: '/ai-tasks',
      gradient: 'from-purple-500 to-indigo-500'
    },
    {
      title: 'Task Management',
      description: 'Organize and prioritize your tasks with advanced tools.',
      icon: <CalendarIcon className="w-8 h-8" />,
      to: '/tasks',
      gradient: 'from-blue-500 to-cyan-500'
    },
    {
      title: 'Daily Challenges',
      description: 'Stay motivated with gamified productivity missions.',
      icon: <RocketIcon className="w-8 h-8" />,
      to: '/challenges',
      gradient: 'from-green-500 to-emerald-500'
    },
    {
      title: 'Skill Building',
      description: 'Accelerate learning with curated content and tracking.',
      icon: <GraduationCapIcon className="w-8 h-8" />,
      to: '/skills',
      gradient: 'from-violet-500 to-purple-500'
    },
  ];

  const stats = [
    { label: 'Tasks Completed', value: '24', icon: Target, color: 'text-green-400' },
    { label: 'Focus Time', value: '4.2h', icon: Clock, color: 'text-primary-400' },
    { label: 'Streak Days', value: '12', icon: TrendingUp, color: 'text-accent-400' },
    { label: 'Level', value: '7', icon: Award, color: 'text-secondary-400' },
  ];

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  return (
    <div className="min-h-screen bg-hero text-white relative overflow-hidden">
      {/* Enhanced Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-dark-900/40 via-primary-900/20 to-secondary-900/40" />
      <div className="absolute inset-0 bg-[linear-gradient(rgba(251,191,36,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(251,191,36,0.02)_1px,transparent_1px)] bg-[size:80px_80px]" />

      {/* Content */}
      <div className="relative z-10">
        <Navbar />

        <div className="pt-28 pb-20 px-6 max-w-7xl mx-auto">
          {/* Enhanced Welcome Section */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-20"
          >
            <motion.div
              className="inline-flex items-center gap-4 glass-card px-8 py-4 rounded-full mb-8 hover:glass-hover transition-all duration-300"
              whileHover={{ scale: 1.05, y: -2 }}
            >
              <motion.div
                animate={{
                  rotate: [0, 10, -10, 0],
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <User size={24} className="text-primary-400" />
              </motion.div>
              <span className="text-white/90 font-semibold text-lg">
                {getGreeting()}, {userName}!
              </span>
              <motion.div
                className="w-3 h-3 bg-success-400 rounded-full"
                animate={{
                  scale: [1, 1.3, 1],
                  opacity: [1, 0.7, 1]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
            </motion.div>

            <motion.h1
              className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-display font-bold mb-8 leading-tight"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.8 }}
            >
              Ready to be
              <motion.span
                className="text-gradient bg-gradient-to-r from-primary-400 via-secondary-400 to-accent-400 bg-clip-text text-transparent block mt-2"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.4, duration: 0.8 }}
              >
                Productive?
              </motion.span>
            </motion.h1>

            <motion.p
              className="text-lg md:text-xl text-white/80 max-w-3xl mx-auto mb-10 font-medium"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.8 }}
            >
              Choose your productivity tool and start making meaningful progress towards your goals today.
            </motion.p>

            {/* Enhanced Current Time */}
            <motion.div
              className="inline-flex items-center gap-3 glass-card px-6 py-3 rounded-full text-white/70 text-sm font-semibold"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.8 }}
              whileHover={{ scale: 1.05 }}
            >
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 60, repeat: Infinity, ease: "linear" }}
              >
                <Clock size={18} />
              </motion.div>
              <span>{currentTime.toLocaleTimeString()}</span>
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 1, repeat: Infinity, ease: "easeInOut" }}
                className="w-2 h-2 bg-primary-400 rounded-full"
              />
            </motion.div>
          </motion.div>

          {/* Enhanced Stats Cards */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-20"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                className="card-glass p-8 text-center group hover:shadow-glass-hover transition-all duration-300 relative overflow-hidden"
                whileHover={{ y: -8, scale: 1.02 }}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 * index, duration: 0.6 }}
              >
                {/* Background Gradient */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-br from-primary-400/5 via-secondary-400/5 to-accent-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"
                  whileHover={{ opacity: 1 }}
                />

                {/* Icon Container */}
                <motion.div
                  className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-white/15 to-white/5 mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg relative`}
                  whileHover={{ rotate: 10 }}
                >
                  <motion.div
                    animate={{
                      scale: [1, 1.1, 1],
                      rotate: [0, 5, -5, 0]
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      delay: index * 0.5,
                      ease: "easeInOut"
                    }}
                  >
                    <stat.icon className={`w-8 h-8 ${stat.color}`} />
                  </motion.div>

                  {/* Glow Effect */}
                  <motion.div
                    className="absolute inset-0 rounded-2xl bg-gradient-to-br from-primary-400/20 to-secondary-400/20 opacity-0 group-hover:opacity-100 blur-lg transition-opacity duration-500"
                    whileHover={{ opacity: 1 }}
                  />
                </motion.div>

                {/* Stats Content */}
                <motion.div
                  className="text-3xl md:text-4xl font-bold text-white mb-2 group-hover:text-primary-200 transition-colors duration-300"
                  whileHover={{ scale: 1.05 }}
                >
                  {stat.value}
                </motion.div>
                <div className="text-white/70 text-sm font-semibold group-hover:text-white/90 transition-colors duration-300">
                  {stat.label}
                </div>

                {/* Floating Accent */}
                <motion.div
                  className="absolute -top-2 -right-2 w-4 h-4 bg-gradient-to-br from-primary-400 to-secondary-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  animate={{
                    rotate: 360,
                    scale: [1, 1.2, 1]
                  }}
                  transition={{
                    rotate: { duration: 8, repeat: Infinity, ease: "linear" },
                    scale: { duration: 2, repeat: Infinity, ease: "easeInOut" }
                  }}
                />
              </motion.div>
            ))}
          </motion.div>

          {/* Feature Cards Grid */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="grid gap-8 md:grid-cols-2 lg:grid-cols-3"
          >
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{
                  duration: 0.6,
                  delay: 0.1 * index,
                  ease: "easeOut"
                }}
              >
                <FeatureCard {...feature} />
              </motion.div>
            ))}
          </motion.div>

          {/* Enhanced Quick Actions */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="mt-20"
          >
            {/* Section Header */}
            <motion.div
              className="text-center mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.9, duration: 0.6 }}
            >
              <h3 className="text-2xl md:text-3xl font-display font-bold text-white mb-4">
                Quick Actions
              </h3>
              <p className="text-white/70 font-medium">
                Jump right into your productivity flow
              </p>
            </motion.div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
              <Link to="/pomodoro">
                <motion.button
                  className="btn-primary px-8 py-4 flex items-center gap-3 text-lg font-bold shadow-button-hover min-w-[220px]"
                  whileHover={{ scale: 1.08, y: -3 }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 1.0, duration: 0.6 }}
                >
                  <motion.div
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  >
                    <Zap size={24} />
                  </motion.div>
                  Start Pomodoro
                </motion.button>
              </Link>

              <Link to="/tasks">
                <motion.button
                  className="btn-glass px-8 py-4 flex items-center gap-3 text-lg font-bold border-2 border-white/20 hover:border-white/40 min-w-[220px]"
                  whileHover={{ scale: 1.08, y: -3 }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 1.1, duration: 0.6 }}
                >
                  <motion.div
                    animate={{
                      scale: [1, 1.2, 1],
                      rotate: [0, 10, -10, 0]
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <Target size={24} />
                  </motion.div>
                  Add New Task
                </motion.button>
              </Link>
            </div>

            {/* Additional Quick Links */}
            <motion.div
              className="flex items-center justify-center gap-8 mt-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2, duration: 0.6 }}
            >
              {[
                { to: '/skills', label: 'Skills', emoji: '🎓' },
                { to: '/mood', label: 'Mood', emoji: '😊' },
                { to: '/challenges', label: 'Challenges', emoji: '🏆' },
              ].map((link, index) => (
                <Link key={link.to} to={link.to}>
                  <motion.div
                    className="flex flex-col items-center gap-2 p-4 rounded-xl glass-card hover:glass-hover transition-all duration-300 group"
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.3 + index * 0.1, duration: 0.5 }}
                  >
                    <motion.div
                      className="text-2xl"
                      animate={{
                        rotate: [0, 10, -10, 0],
                        scale: [1, 1.1, 1]
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        delay: index * 0.5,
                        ease: "easeInOut"
                      }}
                    >
                      {link.emoji}
                    </motion.div>
                    <span className="text-white/80 text-sm font-semibold group-hover:text-white transition-colors duration-300">
                      {link.label}
                    </span>
                  </motion.div>
                </Link>
              ))}
            </motion.div>
          </motion.div>
        </div>

        <Footer />
      </div>
    </div>
  );
}
