@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    box-sizing: border-box;
  }

  body {
    @apply bg-gradient-to-br from-yellow-50 to-amber-50 font-sans antialiased;
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Enhanced Glass morphism utilities */
  .glass {
    @apply bg-white/10 backdrop-blur-lg border border-white/20 shadow-glass;
  }

  .glass-hover {
    @apply bg-white/15 backdrop-blur-lg border border-white/30 shadow-glass-hover;
  }

  .glass-dark {
    @apply bg-black/10 backdrop-blur-lg border border-white/10;
  }

  .glass-card {
    @apply bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl shadow-glass;
  }

  /* Enhanced Modern button styles */
  .btn-primary {
    @apply bg-button-gradient text-white font-semibold py-3 px-6 rounded-xl shadow-button hover:shadow-button-hover transform hover:scale-105 transition-all duration-300 ease-out;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-secondary-500 to-secondary-600 hover:from-secondary-600 hover:to-secondary-700 text-white font-semibold py-3 px-6 rounded-xl shadow-button hover:shadow-button-hover transform hover:scale-105 transition-all duration-300 ease-out;
  }

  .btn-success {
    @apply bg-success-gradient text-white font-semibold py-3 px-6 rounded-xl shadow-glow-success hover:shadow-glow-success transform hover:scale-105 transition-all duration-300 ease-out;
  }

  .btn-warning {
    @apply bg-warning-gradient text-white font-semibold py-3 px-6 rounded-xl shadow-glow-warning hover:shadow-glow-warning transform hover:scale-105 transition-all duration-300 ease-out;
  }

  .btn-error {
    @apply bg-error-gradient text-white font-semibold py-3 px-6 rounded-xl shadow-glow-error hover:shadow-glow-error transform hover:scale-105 transition-all duration-300 ease-out;
  }

  .btn-glass {
    @apply glass text-white font-semibold py-3 px-6 rounded-xl hover:glass-hover transform hover:scale-105 transition-all duration-300 ease-out;
  }

  /* Enhanced Card styles */
  .card-glass {
    @apply glass-card p-6 transition-all duration-300 ease-out hover:shadow-glass-hover hover:bg-white/10;
  }

  .card-modern {
    @apply bg-white rounded-2xl shadow-card hover:shadow-card-hover transition-all duration-300 ease-out;
  }

  .card-feature {
    @apply glass-card p-8 group hover:bg-white/15 transition-all duration-500 ease-out transform hover:scale-105 hover:shadow-glass-hover;
  }

  /* Enhanced Input styles */
  .input-modern {
    @apply w-full px-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-transparent transition-all duration-300;
  }

  .input-glass {
    @apply w-full px-4 py-3 glass text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-white/40 transition-all duration-300;
  }

  /* Enhanced Text gradient utilities */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-400 via-secondary-400 to-accent-400 bg-clip-text text-transparent;
  }

  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent;
  }

  .text-gradient-accent {
    @apply bg-gradient-to-r from-accent-400 to-secondary-400 bg-clip-text text-transparent;
  }

  .text-gradient-success {
    @apply bg-gradient-to-r from-success-400 to-success-600 bg-clip-text text-transparent;
  }

  /* Enhanced Background utilities */
  .bg-mesh {
    @apply bg-mesh-gradient;
    background-size: 400% 400%;
    animation: gradientXY 10s ease infinite;
  }

  .bg-hero {
    @apply bg-hero-gradient;
    background-size: 400% 400%;
    animation: gradientXY 12s ease infinite;
  }

  .bg-animated {
    @apply bg-gradient-to-br from-primary-400 via-secondary-500 to-accent-400;
    background-size: 400% 400%;
    animation: gradientXY 8s ease infinite;
  }
}

@layer utilities {
  /* Custom scrollbar */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Smooth transitions */
  .transition-smooth {
    @apply transition-all duration-300 ease-in-out;
  }

  /* Hover lift effect */
  .hover-lift {
    @apply transform hover:scale-105 hover:-translate-y-1 transition-all duration-200;
  }

  /* Glow effects */
  .glow-primary {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.4);
  }

  .glow-secondary {
    box-shadow: 0 0 20px rgba(255, 204, 0, 0.4);
  }

  /* Text shadows */
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Calendar specific styles */
  .calendar-day {
    @apply relative p-2 min-h-[60px] cursor-pointer rounded-lg transition-all duration-200;
  }

  .calendar-day:hover {
    @apply bg-white/10;
  }

  .calendar-day.today {
    @apply bg-primary-500/20 border border-primary-500/50;
  }

  .calendar-day.selected {
    @apply bg-secondary-500/20 border border-secondary-500/50;
  }

  .calendar-day.other-month {
    @apply text-white/40;
  }

  /* Task priority colors */
  .priority-urgent {
    @apply text-red-400 bg-red-500/20;
  }

  .priority-high {
    @apply text-accent-400 bg-accent-500/20;
  }

  .priority-medium {
    @apply text-secondary-400 bg-secondary-500/20;
  }

  .priority-low {
    @apply text-green-400 bg-green-500/20;
  }

  /* Task status colors */
  .status-completed {
    @apply text-green-400 bg-green-500/20;
  }

  .status-in-progress {
    @apply text-primary-400 bg-primary-500/20;
  }

  .status-pending {
    @apply text-gray-400 bg-gray-500/20;
  }

  .status-revise {
    @apply text-secondary-400 bg-secondary-500/20;
  }
}