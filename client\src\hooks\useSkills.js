// src/hooks/useSkills.js
import { useState, useEffect, useCallback } from 'react';
import { skillService } from '../services/skillService';
import { sampleSkills } from '../data/sampleSkills';

export const useSkills = (userId) => {
  const [skills, setSkills] = useState([]);
  const [userProgress, setUserProgress] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    category: 'all',
    level: 'all',
    progress: 'all',
    search: '',
  });

  // Get skill categories
  const categories = skillService.getSkillCategories();

  // Fetch all skills
  const fetchSkills = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Try to fetch from API first
      try {
        const response = await skillService.getAllSkills();
        setSkills(Array.isArray(response.skills) ? response.skills : []);
      } catch (apiError) {
        // If API fails, use sample data
        console.log('API not available, using sample data');
        setSkills(sampleSkills);
      }
    } catch (err) {
      setError(err.message);
      setSkills(sampleSkills); // Fallback to sample data
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch user progress
  const fetchUserProgress = useCallback(async () => {
    if (!userId) return;
    
    try {
      const response = await skillService.getUserSkillProgress(userId);
      setUserProgress(Array.isArray(response.progress) ? response.progress : []);
    } catch (err) {
      console.error('Error fetching user progress:', err);
      setUserProgress([]);
    }
  }, [userId]);

  // Create a new skill
  const createSkill = async (skillData) => {
    try {
      const response = await skillService.createSkill({
        ...skillData,
        userId,
      });
      await fetchSkills();
      return response;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  // Update skill progress
  const updateSkillProgress = async (skillId, progressData) => {
    try {
      const response = await skillService.updateSkillProgress(skillId, progressData);
      await fetchSkills();
      await fetchUserProgress();
      return response;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  // Create skill progress entry
  const createSkillProgress = async (progressData) => {
    try {
      const response = await skillService.createSkillProgress({
        ...progressData,
        userId,
      });
      await fetchUserProgress();
      return response;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  // Delete a skill
  const deleteSkill = async (skillId) => {
    try {
      await skillService.deleteSkill(skillId);
      await fetchSkills();
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  // Filter skills based on current filters
  const filteredSkills = skills.filter(skill => {
    // Category filter
    if (filters.category !== 'all' && skill.category !== filters.category) {
      return false;
    }

    // Level filter
    if (filters.level !== 'all' && skill.level !== filters.level) {
      return false;
    }

    // Progress filter
    if (filters.progress !== 'all') {
      if (filters.progress === 'not-started' && skill.progress > 0) return false;
      if (filters.progress === 'in-progress' && (skill.progress === 0 || skill.progress === 100)) return false;
      if (filters.progress === 'completed' && skill.progress !== 100) return false;
    }

    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      const nameMatch = skill.name?.toLowerCase().includes(searchLower);
      const descMatch = skill.description?.toLowerCase().includes(searchLower);
      const categoryMatch = skill.category?.toLowerCase().includes(searchLower);
      const subTopicMatch = skill.subTopic?.toLowerCase().includes(searchLower);
      
      if (!nameMatch && !descMatch && !categoryMatch && !subTopicMatch) {
        return false;
      }
    }

    return true;
  });

  // Group skills by category
  const groupedSkills = filteredSkills.reduce((acc, skill) => {
    const category = skill.category || 'Other';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(skill);
    return acc;
  }, {});

  // Get skill statistics
  const stats = {
    total: skills.length,
    completed: skills.filter(s => s.progress === 100).length,
    inProgress: skills.filter(s => s.progress > 0 && s.progress < 100).length,
    notStarted: skills.filter(s => s.progress === 0).length,
    totalProgress: skills.length > 0 ? Math.round(skills.reduce((sum, s) => sum + s.progress, 0) / skills.length) : 0,
    activeStreaks: skills.filter(s => s.streak > 0).length,
    longestStreak: Math.max(...skills.map(s => s.streak || 0), 0),
  };

  // Get skills by category for display
  const getSkillsByCategory = (categoryId) => {
    return filteredSkills.filter(skill => skill.category === categoryId);
  };

  // Get user progress for a specific skill
  const getSkillProgress = (skillId) => {
    return userProgress.find(progress => progress.skillId === skillId);
  };

  // Load data on mount
  useEffect(() => {
    fetchSkills();
  }, [fetchSkills]);

  useEffect(() => {
    fetchUserProgress();
  }, [fetchUserProgress]);

  return {
    skills: filteredSkills,
    groupedSkills,
    userProgress,
    categories,
    stats,
    loading,
    error,
    filters,
    setFilters,
    createSkill,
    updateSkillProgress,
    createSkillProgress,
    deleteSkill,
    getSkillsByCategory,
    getSkillProgress,
    refetch: fetchSkills,
  };
};
