// src/components/SkillProgress.jsx
import { motion } from 'framer-motion';
import { TrendingUp, Target, Award, Calendar, Flame, Clock } from 'lucide-react';

const SkillProgress = ({ skill, userProgress, onUpdateProgress }) => {
  const progressPercentage = skill.progress || 0;
  
  // Calculate progress level
  const getProgressLevel = (progress) => {
    if (progress === 0) return { level: 'Not Started', color: 'text-gray-400' };
    if (progress < 25) return { level: 'Beginner', color: 'text-red-400' };
    if (progress < 50) return { level: 'Learning', color: 'text-accent-400' };
    if (progress < 75) return { level: 'Intermediate', color: 'text-secondary-400' };
    if (progress < 100) return { level: 'Advanced', color: 'text-primary-400' };
    return { level: 'Mastered', color: 'text-green-400' };
  };

  const progressLevel = getProgressLevel(progressPercentage);

  // Get category style
  const getCategoryStyle = (category) => {
    const styles = {
      programming: { color: 'from-primary-500 to-accent-500' },
      languages: { color: 'from-secondary-500 to-primary-500' },
      productivity: { color: 'from-accent-500 to-secondary-500' },
      creative: { color: 'from-primary-400 to-secondary-400' },
      health: { color: 'from-green-500 to-primary-500' },
      professional: { color: 'from-secondary-600 to-accent-600' },
    };
    return styles[category?.toLowerCase()] || { color: 'from-primary-500 to-secondary-500' };
  };

  const categoryStyle = getCategoryStyle(skill.category);

  // Format date
  const formatDate = (date) => {
    if (!date) return 'Never';
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Calculate days since last study
  const getDaysSinceLastStudy = (date) => {
    if (!date) return null;
    const now = new Date();
    const lastStudy = new Date(date);
    const diffTime = Math.abs(now - lastStudy);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const daysSinceLastStudy = getDaysSinceLastStudy(skill.lastStudiedAt);

  return (
    <motion.div
      className="card-glass p-6 space-y-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold text-white mb-1">
            {skill.name || skill.subTopic}
          </h3>
          <p className="text-white/60 capitalize">{skill.category}</p>
        </div>
        <div className={`px-3 py-1 rounded-full text-sm font-medium ${progressLevel.color} bg-current bg-opacity-10`}>
          {progressLevel.level}
        </div>
      </div>

      {/* Main Progress Circle */}
      <div className="flex items-center justify-center">
        <div className="relative w-32 h-32">
          {/* Background Circle */}
          <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
            <circle
              cx="60"
              cy="60"
              r="50"
              stroke="rgba(255, 255, 255, 0.1)"
              strokeWidth="8"
              fill="none"
            />
            {/* Progress Circle */}
            <motion.circle
              cx="60"
              cy="60"
              r="50"
              stroke="url(#progressGradient)"
              strokeWidth="8"
              fill="none"
              strokeLinecap="round"
              strokeDasharray={`${2 * Math.PI * 50}`}
              initial={{ strokeDashoffset: 2 * Math.PI * 50 }}
              animate={{ 
                strokeDashoffset: 2 * Math.PI * 50 * (1 - progressPercentage / 100)
              }}
              transition={{ duration: 1.5, ease: "easeInOut" }}
            />
            <defs>
              <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#3B82F6" />
                <stop offset="100%" stopColor="#06B6D4" />
              </linearGradient>
            </defs>
          </svg>
          
          {/* Center Text */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="text-2xl font-bold text-white">
                {progressPercentage}%
              </div>
              <div className="text-xs text-white/60">Complete</div>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Stats */}
      <div className="grid grid-cols-2 gap-4">
        <div className="text-center p-4 bg-white/5 rounded-lg">
          <div className="flex items-center justify-center gap-2 text-accent-400 mb-2">
            <Flame size={20} />
            <span className="text-xl font-bold">{skill.streak || 0}</span>
          </div>
          <p className="text-sm text-white/60">Day Streak</p>
        </div>

        <div className="text-center p-4 bg-white/5 rounded-lg">
          <div className="flex items-center justify-center gap-2 text-primary-400 mb-2">
            <Target size={20} />
            <span className="text-xl font-bold">{skill.level}</span>
          </div>
          <p className="text-sm text-white/60">Current Level</p>
        </div>
      </div>

      {/* Timeline Info */}
      <div className="space-y-3">
        <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
          <div className="flex items-center gap-3">
            <Calendar className="text-green-400" size={16} />
            <span className="text-sm text-white/80">Last Studied</span>
          </div>
          <span className="text-sm text-white font-medium">
            {formatDate(skill.lastStudiedAt)}
          </span>
        </div>

        {daysSinceLastStudy && daysSinceLastStudy > 1 && (
          <div className="flex items-center justify-between p-3 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
            <div className="flex items-center gap-3">
              <Clock className="text-yellow-400" size={16} />
              <span className="text-sm text-yellow-200">Days Since Study</span>
            </div>
            <span className="text-sm text-yellow-400 font-medium">
              {daysSinceLastStudy} days
            </span>
          </div>
        )}

        <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
          <div className="flex items-center gap-3">
            <TrendingUp className="text-purple-400" size={16} />
            <span className="text-sm text-white/80">Created</span>
          </div>
          <span className="text-sm text-white font-medium">
            {formatDate(skill.createdAt)}
          </span>
        </div>
      </div>

      {/* Action Button */}
      <motion.button
        onClick={() => onUpdateProgress?.(skill)}
        className={`w-full py-3 px-4 rounded-lg font-medium bg-gradient-to-r ${categoryStyle.color} text-white hover:shadow-lg transition-all duration-200`}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        Update Progress
      </motion.button>

      {/* AI Suggestions */}
      {skill.aiSuggestions && skill.aiSuggestions.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-white/80 flex items-center gap-2">
            <Award size={16} className="text-yellow-400" />
            AI Suggestions
          </h4>
          <div className="space-y-2">
            {skill.aiSuggestions.slice(0, 3).map((suggestion, index) => (
              <div key={index} className="p-3 bg-white/5 rounded-lg border-l-2 border-yellow-400/50">
                <p className="text-sm text-white/70">{suggestion}</p>
              </div>
            ))}
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default SkillProgress;
