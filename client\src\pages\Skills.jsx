// src/pages/Skills.jsx
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Plus, 
  BookOpen, 
  TrendingUp, 
  Target, 
  Award,
  Grid3X3,
  List,
  Filter,
  Sparkles
} from 'lucide-react';
import Navbar from '../components/Navbar1';
import Footer from '../components/Footer';
import SkillCard from '../components/SkillCard';
import SkillFilters from '../components/SkillFilters';
import SkillProgress from '../components/SkillProgress';
import SkillForm from '../components/SkillForm';
import SkillCategories from '../components/SkillCategories';
import { useSkills } from '../hooks/useSkills';
import clsx from 'clsx';

export default function Skills() {
  const [currentUser] = useState({ _id: '507f1f77bcf86cd799439011' }); // Mock user ID
  const [view, setView] = useState('grid'); // 'grid', 'list', 'categories'
  const [selectedSkill, setSelectedSkill] = useState(null);
  const [showAddSkill, setShowAddSkill] = useState(false);
  const [activeSkill, setActiveSkill] = useState(null);

  const {
    skills,
    groupedSkills,
    categories,
    stats,
    loading,
    error,
    filters,
    setFilters,
    createSkill,
    updateSkillProgress,
    getSkillsByCategory
  } = useSkills(currentUser._id);

  // Handle skill actions
  const handleStartSkill = (skill) => {
    setActiveSkill(skill);
    // Here you could start a timer or navigate to skill practice
    console.log('Starting skill:', skill.name);
  };

  const handlePauseSkill = (skill) => {
    setActiveSkill(null);
    console.log('Pausing skill:', skill.name);
  };

  const handleUpdateProgress = (skill) => {
    setSelectedSkill(skill);
  };

  const handleAddSkill = async (skillData) => {
    try {
      await createSkill(skillData);
      setShowAddSkill(false);
    } catch (error) {
      console.error('Error adding skill:', error);
    }
  };

  // Get greeting based on time
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-mesh text-white flex items-center justify-center">
        <div className="text-center">
          <motion.div
            className="w-16 h-16 border-4 border-primary-500 border-t-transparent rounded-full mx-auto mb-4"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          />
          <p className="text-white/60">Loading your skills...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-mesh text-white relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-dark-900/50 via-primary-900/30 to-secondary-900/50" />

      {/* Content */}
      <div className="relative z-10">
        <Navbar />

        <div className="pt-32 pb-20 px-6 max-w-7xl mx-auto">
          {/* Header Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            <motion.div
              className="inline-flex items-center gap-3 glass px-6 py-3 rounded-full mb-6"
              whileHover={{ scale: 1.05 }}
            >
              <BookOpen size={20} className="text-primary-400" />
              <span className="text-white/90 font-medium">
                {getGreeting()}! Ready to learn?
              </span>
              <Sparkles size={16} className="text-yellow-400" />
            </motion.div>

            <h1 className="text-4xl md:text-5xl lg:text-6xl font-display font-bold mb-6">
              Your
              <span className="text-gradient block mt-2">Skill Journey</span>
            </h1>

            <p className="text-xl text-white/70 max-w-2xl mx-auto mb-8">
              Master new skills, track your progress, and unlock your potential with our 
              intelligent learning system.
            </p>

            {/* Quick Actions */}
            <div className="flex flex-wrap items-center justify-center gap-4">
              <motion.button
                onClick={() => setShowAddSkill(true)}
                className="btn-primary px-6 py-3 flex items-center gap-2"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Plus size={20} />
                Add New Skill
              </motion.button>

              <div className="flex items-center gap-2 glass px-4 py-2 rounded-lg">
                <button
                  onClick={() => setView('grid')}
                  className={clsx(
                    'p-2 rounded-lg transition-all duration-200',
                    view === 'grid' 
                      ? 'bg-primary-500 text-white' 
                      : 'text-white/60 hover:text-white hover:bg-white/10'
                  )}
                >
                  <Grid3X3 size={16} />
                </button>
                <button
                  onClick={() => setView('list')}
                  className={clsx(
                    'p-2 rounded-lg transition-all duration-200',
                    view === 'list' 
                      ? 'bg-primary-500 text-white' 
                      : 'text-white/60 hover:text-white hover:bg-white/10'
                  )}
                >
                  <List size={16} />
                </button>
              </div>
            </div>
          </motion.div>

          {/* Filters */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="mb-8"
          >
            <SkillFilters
              filters={filters}
              setFilters={setFilters}
              categories={categories}
              stats={stats}
            />
          </motion.div>

          {/* Skills Grid/List */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            {error && (
              <div className="text-center py-12">
                <p className="text-red-400 mb-4">Error loading skills: {error}</p>
                <button 
                  onClick={() => window.location.reload()}
                  className="btn-primary px-6 py-2"
                >
                  Retry
                </button>
              </div>
            )}

            {!error && skills.length === 0 && (
              <div className="text-center py-12">
                <motion.div
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <BookOpen size={64} className="text-white/20 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">No Skills Found</h3>
                  <p className="text-white/60 mb-6">
                    {filters.search || filters.category !== 'all' || filters.level !== 'all' || filters.progress !== 'all'
                      ? 'Try adjusting your filters or search terms.'
                      : 'Start your learning journey by adding your first skill!'
                    }
                  </p>
                  <button
                    onClick={() => setShowAddSkill(true)}
                    className="btn-primary px-6 py-3 flex items-center gap-2 mx-auto"
                  >
                    <Plus size={20} />
                    Add Your First Skill
                  </button>
                </motion.div>
              </div>
            )}

            {!error && skills.length > 0 && (
              <div className={clsx(
                'grid gap-6',
                view === 'grid' 
                  ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                  : 'grid-cols-1 max-w-4xl mx-auto'
              )}>
                <AnimatePresence>
                  {skills.map((skill, index) => (
                    <motion.div
                      key={skill._id || index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                    >
                      <SkillCard
                        skill={skill}
                        onStart={handleStartSkill}
                        onPause={handlePauseSkill}
                        onUpdateProgress={handleUpdateProgress}
                        isActive={activeSkill?._id === skill._id}
                      />
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            )}
          </motion.div>
        </div>

        <Footer />
      </div>

      {/* Skill Progress Modal */}
      <AnimatePresence>
        {selectedSkill && (
          <motion.div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setSelectedSkill(null)}
          >
            <motion.div
              className="max-w-md w-full"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              <SkillProgress
                skill={selectedSkill}
                onUpdateProgress={(skill) => {
                  // Handle progress update
                  console.log('Update progress for:', skill.name);
                  setSelectedSkill(null);
                }}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Add Skill Modal */}
      <AnimatePresence>
        {showAddSkill && (
          <motion.div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowAddSkill(false)}
          >
            <div onClick={(e) => e.stopPropagation()}>
              <SkillForm
                onSubmit={handleAddSkill}
                onCancel={() => setShowAddSkill(false)}
                categories={categories}
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
