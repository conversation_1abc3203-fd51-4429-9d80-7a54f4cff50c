import { motion } from 'framer-motion';
import { Heart, Github, Twitter, Linkedin, Mail } from 'lucide-react';

export default function Footer() {
  const socialLinks = [
    { icon: Github, href: '#', label: 'GitHub' },
    { icon: Twitter, href: '#', label: 'Twitter' },
    { icon: Linkedin, href: '#', label: 'LinkedIn' },
    { icon: Mail, href: '#', label: 'Email' },
  ];

  return (
    <footer className="relative py-20 px-6 border-t border-white/10">
      {/* Enhanced Background */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-black/10 to-transparent" />
      <div className="absolute inset-0 bg-[linear-gradient(rgba(251,191,36,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(251,191,36,0.02)_1px,transparent_1px)] bg-[size:60px_60px]" />

      <div className="relative max-w-7xl mx-auto">
        {/* Enhanced Main Footer Content */}
        <div className="grid md:grid-cols-3 gap-16 mb-16">
          {/* Enhanced Brand Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center md:text-left"
          >
            <motion.div
              className="flex items-center justify-center md:justify-start gap-3 mb-6"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <motion.span
                className="text-3xl"
                animate={{
                  rotate: [0, 10, -10, 0],
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                🧠
              </motion.span>
              <span className="text-2xl font-display font-bold text-gradient bg-gradient-to-r from-primary-400 via-secondary-400 to-accent-400 bg-clip-text text-transparent">
                Procrastinot
              </span>
            </motion.div>
            <p className="text-white/80 text-base leading-relaxed max-w-sm font-medium">
              Transform your productivity with AI-powered tools designed to help you
              beat procrastination and achieve your goals with confidence.
            </p>

            {/* Enhanced Stats */}
            <div className="mt-8 grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary-400">15K+</div>
                <div className="text-xs text-white/60 font-medium">Happy Users</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-secondary-400">98%</div>
                <div className="text-xs text-white/60 font-medium">Success Rate</div>
              </div>
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-center md:text-left"
          >
            <h3 className="text-white font-bold text-lg mb-6">Quick Links</h3>
            <div className="space-y-3">
              {[
                { name: 'Features', href: '#features', emoji: '⚡' },
                { name: 'Dashboard', href: '/dashboard', emoji: '📊' },
                { name: 'Pomodoro', href: '/pomodoro', emoji: '⏰' },
                { name: 'Tasks', href: '/tasks', emoji: '📋' },
                { name: 'Skills', href: '/skills', emoji: '🎓' },
                { name: 'Support', href: '#support', emoji: '💬' },
              ].map((link) => (
                <motion.a
                  key={link.name}
                  href={link.href}
                  className="block text-white/70 hover:text-white text-sm font-medium transition-colors duration-300 flex items-center gap-2 group"
                  whileHover={{ x: 5 }}
                >
                  <span className="group-hover:scale-110 transition-transform duration-200">
                    {link.emoji}
                  </span>
                  {link.name}
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Connect Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-center md:text-left"
          >
            <h3 className="text-white font-bold text-lg mb-6">Stay Connected</h3>
            <div className="flex justify-center md:justify-start gap-4 mb-6">
              {socialLinks.map(({ icon: Icon, href, label }) => (
                <motion.a
                  key={label}
                  href={href}
                  className="w-12 h-12 glass rounded-full flex items-center justify-center text-white/70 hover:text-white hover:glass-hover transition-all duration-300 group"
                  whileHover={{ scale: 1.15, y: -3 }}
                  whileTap={{ scale: 0.95 }}
                  aria-label={label}
                >
                  <motion.div
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 0.6 }}
                  >
                    <Icon size={20} />
                  </motion.div>
                </motion.a>
              ))}
            </div>
            <p className="text-white/70 text-sm font-medium mb-4">
              Follow us for updates and productivity tips
            </p>

            {/* Newsletter Signup */}
            <motion.div
              className="glass-card p-4 rounded-xl"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.3 }}
            >
              <p className="text-white/80 text-xs font-medium mb-2">
                📧 Get productivity tips weekly
              </p>
              <div className="flex gap-2">
                <input
                  type="email"
                  placeholder="Enter email"
                  className="flex-1 px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white text-xs placeholder-white/50 focus:outline-none focus:border-primary-400 transition-colors duration-300"
                />
                <motion.button
                  className="px-3 py-2 bg-primary-500 text-white text-xs font-semibold rounded-lg hover:bg-primary-600 transition-colors duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Join
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        </div>

        {/* Enhanced Bottom Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="pt-12 border-t border-white/20 flex flex-col md:flex-row items-center justify-between gap-6"
        >
          <div className="text-white/70 text-sm text-center md:text-left font-medium">
            © {new Date().getFullYear()} Procrastinot. Crafted with{' '}
            <motion.span
              className="inline-block text-red-400"
              animate={{
                scale: [1, 1.3, 1],
                rotate: [0, 10, -10, 0]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatDelay: 3,
                ease: "easeInOut"
              }}
            >
              <Heart size={16} className="inline" />
            </motion.span>{' '}
            by passionate developers.
          </div>

          <div className="flex items-center gap-6">
            <div className="text-white/60 text-xs flex items-center gap-3">
              <span>Powered by</span>
              <motion.span
                className="text-primary-400 font-semibold"
                whileHover={{ scale: 1.1 }}
              >
                OpenAI
              </motion.span>
              <span className="text-white/30">·</span>
              <motion.span
                className="text-secondary-400 font-semibold"
                whileHover={{ scale: 1.1 }}
              >
                React
              </motion.span>
              <span className="text-white/30">·</span>
              <motion.span
                className="text-accent-400 font-semibold"
                whileHover={{ scale: 1.1 }}
              >
                Tailwind
              </motion.span>
            </div>

            {/* Back to Top Button */}
            <motion.button
              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
              className="w-10 h-10 glass rounded-full flex items-center justify-center text-white/70 hover:text-white hover:glass-hover transition-all duration-300"
              whileHover={{ scale: 1.1, y: -2 }}
              whileTap={{ scale: 0.95 }}
              aria-label="Back to top"
            >
              <motion.div
                animate={{ y: [0, -2, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              >
                ↑
              </motion.div>
            </motion.button>
          </div>
        </motion.div>
      </div>
    </footer>
  );
}
