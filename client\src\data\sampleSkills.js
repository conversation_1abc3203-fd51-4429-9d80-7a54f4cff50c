// src/data/sampleSkills.js
export const sampleSkills = [
  // Programming Skills
  {
    _id: '1',
    name: 'JavaScript Fundamentals',
    description: 'Master the basics of JavaScript programming language',
    category: 'programming',
    subTopic: 'Web Development',
    level: 'Beginner',
    progress: 65,
    streak: 7,
    lastStudiedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
    aiSuggestions: [
      'Practice array methods like map, filter, and reduce',
      'Build a simple calculator project',
      'Learn about ES6 features like arrow functions'
    ],
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
  },
  {
    _id: '2',
    name: 'React Development',
    description: 'Build modern web applications with React',
    category: 'programming',
    subTopic: 'Frontend Framework',
    level: 'Intermediate',
    progress: 40,
    streak: 3,
    lastStudiedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    aiSuggestions: [
      'Practice component lifecycle methods',
      'Build a todo app with hooks',
      'Learn about state management with Context API'
    ],
    createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
  },
  {
    _id: '3',
    name: 'Python Data Science',
    description: 'Analyze data and build machine learning models',
    category: 'programming',
    subTopic: 'Data Science',
    level: 'Advanced',
    progress: 85,
    streak: 12,
    lastStudiedAt: new Date(Date.now() - 0 * 24 * 60 * 60 * 1000), // Today
    aiSuggestions: [
      'Practice pandas data manipulation',
      'Build a machine learning model with scikit-learn',
      'Learn data visualization with matplotlib'
    ],
    createdAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
  },

  // Language Skills
  {
    _id: '4',
    name: 'Spanish Conversation',
    description: 'Improve conversational Spanish skills',
    category: 'languages',
    subTopic: 'Speaking',
    level: 'Intermediate',
    progress: 55,
    streak: 5,
    lastStudiedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    aiSuggestions: [
      'Practice common phrases for daily conversations',
      'Watch Spanish movies with subtitles',
      'Join a Spanish conversation group'
    ],
    createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
  },
  {
    _id: '5',
    name: 'French Grammar',
    description: 'Master French grammar rules and structures',
    category: 'languages',
    subTopic: 'Grammar',
    level: 'Beginner',
    progress: 25,
    streak: 0,
    lastStudiedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
    aiSuggestions: [
      'Practice verb conjugations daily',
      'Learn gender rules for nouns',
      'Use flashcards for irregular verbs'
    ],
    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
  },

  // Productivity Skills
  {
    _id: '6',
    name: 'Touch Typing',
    description: 'Increase typing speed and accuracy',
    category: 'productivity',
    subTopic: 'Typing Skills',
    level: 'Intermediate',
    progress: 75,
    streak: 15,
    lastStudiedAt: new Date(Date.now() - 0 * 24 * 60 * 60 * 1000), // Today
    aiSuggestions: [
      'Practice typing tests daily for 15 minutes',
      'Focus on accuracy before speed',
      'Learn proper finger positioning'
    ],
    createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
  },
  {
    _id: '7',
    name: 'Time Management',
    description: 'Optimize productivity and manage time effectively',
    category: 'productivity',
    subTopic: 'Organization',
    level: 'Beginner',
    progress: 30,
    streak: 2,
    lastStudiedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    aiSuggestions: [
      'Try the Pomodoro Technique',
      'Use a task management system',
      'Practice saying no to non-essential tasks'
    ],
    createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
  },

  // Creative Skills
  {
    _id: '8',
    name: 'Digital Drawing',
    description: 'Create digital art and illustrations',
    category: 'creative',
    subTopic: 'Digital Art',
    level: 'Beginner',
    progress: 20,
    streak: 1,
    lastStudiedAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000),
    aiSuggestions: [
      'Practice basic shapes and forms',
      'Learn about color theory',
      'Study anatomy and proportions'
    ],
    createdAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000),
  },
  {
    _id: '9',
    name: 'Creative Writing',
    description: 'Develop storytelling and writing skills',
    category: 'creative',
    subTopic: 'Writing',
    level: 'Intermediate',
    progress: 45,
    streak: 8,
    lastStudiedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    aiSuggestions: [
      'Write for 30 minutes daily',
      'Read books in your target genre',
      'Join a writing workshop or group'
    ],
    createdAt: new Date(Date.now() - 40 * 24 * 60 * 60 * 1000),
  },

  // Health & Fitness Skills
  {
    _id: '10',
    name: 'Meditation Practice',
    description: 'Develop mindfulness and reduce stress',
    category: 'health',
    subTopic: 'Mindfulness',
    level: 'Beginner',
    progress: 35,
    streak: 4,
    lastStudiedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    aiSuggestions: [
      'Start with 5-minute daily sessions',
      'Try guided meditation apps',
      'Focus on breath awareness'
    ],
    createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
  },
  {
    _id: '11',
    name: 'Running Endurance',
    description: 'Build cardiovascular fitness and endurance',
    category: 'health',
    subTopic: 'Cardio',
    level: 'Intermediate',
    progress: 60,
    streak: 6,
    lastStudiedAt: new Date(Date.now() - 0 * 24 * 60 * 60 * 1000), // Today
    aiSuggestions: [
      'Follow a structured training plan',
      'Gradually increase distance',
      'Focus on proper running form'
    ],
    createdAt: new Date(Date.now() - 50 * 24 * 60 * 60 * 1000),
  },

  // Professional Skills
  {
    _id: '12',
    name: 'Public Speaking',
    description: 'Improve presentation and communication skills',
    category: 'professional',
    subTopic: 'Communication',
    level: 'Beginner',
    progress: 15,
    streak: 0,
    lastStudiedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
    aiSuggestions: [
      'Practice in front of a mirror',
      'Join a Toastmasters club',
      'Record yourself speaking'
    ],
    createdAt: new Date(Date.now() - 35 * 24 * 60 * 60 * 1000),
  },
  {
    _id: '13',
    name: 'Leadership Skills',
    description: 'Develop team management and leadership abilities',
    category: 'professional',
    subTopic: 'Management',
    level: 'Advanced',
    progress: 80,
    streak: 9,
    lastStudiedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    aiSuggestions: [
      'Read leadership books regularly',
      'Practice active listening',
      'Seek feedback from team members'
    ],
    createdAt: new Date(Date.now() - 70 * 24 * 60 * 60 * 1000),
  }
];
