// src/services/skillService.js
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api';

class SkillService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // Helper method to get auth headers
  getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  // Helper method to handle API responses
  async handleResponse(response) {
    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Network error' }));
      throw new Error(error.message || `HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  // Get all skills
  async getAllSkills() {
    try {
      const response = await fetch(`${this.baseURL}/skills`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching skills:', error);
      throw error;
    }
  }

  // Get skills by category
  async getSkillsByCategory(category) {
    try {
      const response = await fetch(`${this.baseURL}/skills/category/${category}`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching skills by category:', error);
      throw error;
    }
  }

  // Get user's skill progress
  async getUserSkillProgress(userId) {
    try {
      const response = await fetch(`${this.baseURL}/skill-progress/user/${userId}`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error fetching user skill progress:', error);
      throw error;
    }
  }

  // Create a new skill
  async createSkill(skillData) {
    try {
      const response = await fetch(`${this.baseURL}/skills`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(skillData),
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error creating skill:', error);
      throw error;
    }
  }

  // Update skill progress
  async updateSkillProgress(skillId, progressData) {
    try {
      const response = await fetch(`${this.baseURL}/skills/${skillId}`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(progressData),
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error updating skill progress:', error);
      throw error;
    }
  }

  // Create skill progress entry
  async createSkillProgress(progressData) {
    try {
      const response = await fetch(`${this.baseURL}/skill-progress`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(progressData),
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error creating skill progress:', error);
      throw error;
    }
  }

  // Delete a skill
  async deleteSkill(skillId) {
    try {
      const response = await fetch(`${this.baseURL}/skills/${skillId}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders(),
      });
      return this.handleResponse(response);
    } catch (error) {
      console.error('Error deleting skill:', error);
      throw error;
    }
  }

  // Get predefined skill categories and suggestions
  getSkillCategories() {
    return [
      {
        id: 'programming',
        name: 'Programming',
        icon: '💻',
        color: 'from-blue-500 to-cyan-500',
        skills: [
          'JavaScript', 'Python', 'React', 'Node.js', 'TypeScript', 
          'Java', 'C++', 'Go', 'Rust', 'Swift'
        ]
      },
      {
        id: 'languages',
        name: 'Languages',
        icon: '🌍',
        color: 'from-green-500 to-emerald-500',
        skills: [
          'Spanish', 'French', 'German', 'Italian', 'Portuguese',
          'Japanese', 'Korean', 'Mandarin', 'Arabic', 'Russian'
        ]
      },
      {
        id: 'productivity',
        name: 'Productivity',
        icon: '⚡',
        color: 'from-orange-500 to-red-500',
        skills: [
          'Typing Speed', 'Time Management', 'Note Taking', 'Speed Reading',
          'Memory Techniques', 'Focus Training', 'Organization', 'Planning'
        ]
      },
      {
        id: 'creative',
        name: 'Creative',
        icon: '🎨',
        color: 'from-purple-500 to-pink-500',
        skills: [
          'Drawing', 'Writing', 'Photography', 'Video Editing', 'Music',
          'Design', 'Animation', 'Creative Writing', 'Storytelling'
        ]
      },
      {
        id: 'health',
        name: 'Health & Fitness',
        icon: '🏃‍♂️',
        color: 'from-teal-500 to-green-500',
        skills: [
          'Meditation', 'Yoga', 'Running', 'Strength Training', 'Nutrition',
          'Sleep Hygiene', 'Stress Management', 'Mindfulness', 'Breathing'
        ]
      },
      {
        id: 'professional',
        name: 'Professional',
        icon: '💼',
        color: 'from-indigo-500 to-purple-500',
        skills: [
          'Public Speaking', 'Leadership', 'Communication', 'Networking',
          'Project Management', 'Sales', 'Marketing', 'Negotiation'
        ]
      }
    ];
  }
}

export const skillService = new SkillService();
