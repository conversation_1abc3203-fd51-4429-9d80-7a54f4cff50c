// src/components/Navbar1.jsx
import { Link, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Moon,
  Bell,
  User,
  Settings,
  LogOut,
  Menu,
  X,
  Home,
  CheckSquare,
  Timer,
  BookOpen,
  Trophy
} from "lucide-react";

export default function Navbar() {
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [notifications] = useState(3); // Mock notification count

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleProfileClick = () => {
    navigate("/profile");
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    navigate('/');
  };

  const navLinks = [
    { to: "/dashboard", label: "Home", icon: Home },
    { to: "/tasks", label: "Tasks", icon: CheckSquare },
    { to: "/pomodoro", label: "Pomodoro", icon: Timer },
    { to: "/skills", label: "Skills", icon: BookOpen },
    { to: "/challenges", label: "Challenges", icon: Trophy },
  ];

  return (
    <motion.nav
      className={`fixed top-0 w-full z-50 transition-all duration-300 ${
        scrolled
          ? 'glass border-b border-white/10 shadow-glass'
          : 'bg-transparent'
      }`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="max-w-7xl mx-auto px-6 py-4 flex items-center justify-between">

        {/* Enhanced Left: Logo */}
        <motion.div
          whileHover={{ scale: 1.08, y: -2 }}
          whileTap={{ scale: 0.95 }}
          className="relative"
        >
          <Link
            to="/dashboard"
            className="flex items-center space-x-3 text-xl font-display font-bold group"
          >
            <motion.div
              className="relative"
              animate={{
                rotate: [0, 15, -15, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                repeatDelay: 3,
                ease: "easeInOut"
              }}
            >
              <span className="text-3xl drop-shadow-lg">🧠</span>
              <motion.div
                className="absolute inset-0 bg-primary-400/20 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              />
            </motion.div>
            <motion.span
              className="text-gradient bg-gradient-to-r from-primary-400 via-secondary-400 to-accent-400 bg-clip-text text-transparent font-black tracking-tight"
              whileHover={{ scale: 1.05 }}
            >
              Procrastinot
            </motion.span>
          </Link>
        </motion.div>

        {/* Enhanced Center: Navigation Links - Desktop */}
        <div className="hidden lg:flex items-center space-x-2">
          {navLinks.map(({ to, label, icon: Icon }) => (
            <motion.div
              key={to}
              whileHover={{ scale: 1.08, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link
                to={to}
                className="group flex items-center gap-3 px-5 py-3 rounded-xl text-white/80 hover:text-white hover:glass-hover transition-all duration-300 font-semibold relative"
              >
                <motion.div
                  whileHover={{ rotate: 10, scale: 1.1 }}
                  transition={{ duration: 0.2 }}
                >
                  <Icon size={20} />
                </motion.div>
                <span className="hidden xl:block relative">
                  {label}
                  <motion.div
                    className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary-400 to-secondary-400 group-hover:w-full transition-all duration-300"
                    layoutId="nav-underline-auth"
                  />
                </span>
              </Link>
            </motion.div>
          ))}
        </div>

        {/* Enhanced Right: Actions */}
        <div className="flex items-center gap-4">
          {/* Enhanced Notifications */}
          <motion.div
            className="relative"
            whileHover={{ scale: 1.15, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <button className="p-3 rounded-xl glass hover:glass-hover text-white/80 hover:text-white transition-all duration-300 group">
              <motion.div
                whileHover={{ rotate: 15 }}
                transition={{ duration: 0.2 }}
              >
                <Bell size={22} />
              </motion.div>
              {notifications > 0 && (
                <motion.span
                  className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-r from-error-500 to-error-600 text-white text-xs rounded-full flex items-center justify-center font-bold shadow-glow-error"
                  initial={{ scale: 0 }}
                  animate={{
                    scale: 1,
                    pulse: [1, 1.2, 1]
                  }}
                  transition={{
                    scale: { type: "spring", stiffness: 500, damping: 30 },
                    pulse: { duration: 2, repeat: Infinity, ease: "easeInOut" }
                  }}
                >
                  {notifications}
                </motion.span>
              )}
            </button>
          </motion.div>

          {/* Enhanced Theme Toggle */}
          <motion.button
            className="p-3 rounded-xl glass hover:glass-hover text-white/80 hover:text-white transition-all duration-300"
            whileHover={{ scale: 1.15, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <motion.div
              whileHover={{ rotate: 180 }}
              transition={{ duration: 0.3 }}
            >
              <Moon size={22} />
            </motion.div>
          </motion.button>

          {/* Enhanced Profile Dropdown */}
          <div className="relative">
            <motion.button
              onClick={handleProfileClick}
              className="flex items-center gap-3 p-2 rounded-xl glass hover:glass-hover transition-all duration-300 group"
              whileHover={{ scale: 1.08, y: -2 }}
              whileTap={{ scale: 0.95 }}
            >
              <motion.div
                className="w-10 h-10 rounded-full bg-gradient-to-br from-primary-400 via-secondary-400 to-accent-400 flex items-center justify-center text-white font-bold text-sm shadow-lg"
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.6 }}
              >
                C
              </motion.div>
              <span className="hidden md:block text-white/80 font-semibold group-hover:text-white transition-colors duration-300">
                Chethan
              </span>
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="hidden md:block text-white/60"
              >
                ⚡
              </motion.div>
            </motion.button>
          </div>

          {/* Enhanced Mobile Menu Toggle */}
          <motion.button
            className="lg:hidden p-3 rounded-xl glass hover:glass-hover text-white/80 hover:text-white transition-all duration-300"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            whileHover={{ scale: 1.15, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <motion.div
              animate={{ rotate: isMenuOpen ? 180 : 0 }}
              transition={{ duration: 0.3 }}
            >
              {isMenuOpen ? <X size={22} /> : <Menu size={22} />}
            </motion.div>
          </motion.button>
        </div>
      </div>

      {/* Mobile Menu */}
      <motion.div
        className={`lg:hidden ${isMenuOpen ? 'block' : 'hidden'}`}
        initial={{ opacity: 0, height: 0 }}
        animate={{
          opacity: isMenuOpen ? 1 : 0,
          height: isMenuOpen ? 'auto' : 0
        }}
        transition={{ duration: 0.3 }}
      >
        <div className="glass border-t border-white/10 px-6 py-4">
          <div className="space-y-2">
            {navLinks.map(({ to, label, icon: Icon }) => (
              <motion.div key={to} whileTap={{ scale: 0.95 }}>
                <Link
                  to={to}
                  className="flex items-center gap-3 px-4 py-3 rounded-xl text-white/80 hover:text-white hover:bg-white/10 transition-all duration-200"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <Icon size={20} />
                  <span>{label}</span>
                </Link>
              </motion.div>
            ))}

            {/* Mobile Profile Actions */}
            <div className="pt-4 border-t border-white/10 space-y-2">
              <motion.button
                className="flex items-center gap-3 px-4 py-3 rounded-xl text-white/80 hover:text-white hover:bg-white/10 transition-all duration-200 w-full"
                whileTap={{ scale: 0.95 }}
                onClick={handleProfileClick}
              >
                <Settings size={20} />
                <span>Settings</span>
              </motion.button>

              <motion.button
                className="flex items-center gap-3 px-4 py-3 rounded-xl text-red-400 hover:text-red-300 hover:bg-red-500/10 transition-all duration-200 w-full"
                whileTap={{ scale: 0.95 }}
                onClick={handleLogout}
              >
                <LogOut size={20} />
                <span>Logout</span>
              </motion.button>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.nav>
  );
}
