// src/components/SkillForm.jsx
import { useState } from 'react';
import { motion } from 'framer-motion';
import { X, Plus, Sparkles } from 'lucide-react';

const SkillForm = ({ onSubmit, onCancel, categories }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: '',
    level: 'Beginner',
    subTopic: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Skill name is required';
    }
    
    if (!formData.category) {
      newErrors.category = 'Please select a category';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setIsSubmitting(true);
    
    try {
      await onSubmit({
        ...formData,
        progress: 0,
        streak: 0,
        lastStudiedAt: null,
        aiSuggestions: []
      });
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get suggested skills based on category
  const getSuggestedSkills = (categoryId) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category ? category.skills : [];
  };

  const suggestedSkills = formData.category ? getSuggestedSkills(formData.category) : [];

  return (
    <motion.div
      className="max-w-lg w-full card-glass p-6"
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      exit={{ scale: 0.8, opacity: 0 }}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
            <Plus size={20} className="text-white" />
          </div>
          <h3 className="text-2xl font-bold text-white">Add New Skill</h3>
        </div>
        <button
          onClick={onCancel}
          className="p-2 text-white/60 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200"
        >
          <X size={20} />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Skill Name */}
        <div>
          <label className="block text-sm font-medium text-white/80 mb-2">
            Skill Name *
          </label>
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            placeholder="e.g., JavaScript, Spanish, Typing..."
            className={`w-full px-4 py-3 bg-white/10 border rounded-lg text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 ${
              errors.name ? 'border-red-500' : 'border-white/20'
            }`}
          />
          {errors.name && (
            <p className="text-red-400 text-sm mt-1">{errors.name}</p>
          )}
        </div>

        {/* Category */}
        <div>
          <label className="block text-sm font-medium text-white/80 mb-2">
            Category *
          </label>
          <select
            name="category"
            value={formData.category}
            onChange={handleChange}
            className={`w-full px-4 py-3 bg-white/10 border rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 ${
              errors.category ? 'border-red-500' : 'border-white/20'
            }`}
          >
            <option value="" className="bg-dark-800">Select a category</option>
            {categories.map(category => (
              <option key={category.id} value={category.id} className="bg-dark-800">
                {category.icon} {category.name}
              </option>
            ))}
          </select>
          {errors.category && (
            <p className="text-red-400 text-sm mt-1">{errors.category}</p>
          )}
        </div>

        {/* Suggested Skills */}
        {suggestedSkills.length > 0 && (
          <div>
            <label className="block text-sm font-medium text-white/80 mb-2 flex items-center gap-2">
              <Sparkles size={16} className="text-yellow-400" />
              Popular Skills in {categories.find(c => c.id === formData.category)?.name}
            </label>
            <div className="flex flex-wrap gap-2">
              {suggestedSkills.slice(0, 6).map((skill, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={() => setFormData(prev => ({ ...prev, name: skill }))}
                  className="px-3 py-1 text-sm bg-white/10 text-white/80 rounded-full hover:bg-white/20 hover:text-white transition-all duration-200"
                >
                  {skill}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Sub Topic */}
        <div>
          <label className="block text-sm font-medium text-white/80 mb-2">
            Sub Topic (Optional)
          </label>
          <input
            type="text"
            name="subTopic"
            value={formData.subTopic}
            onChange={handleChange}
            placeholder="e.g., Web Development, Conversation, Speed..."
            className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
          />
        </div>

        {/* Level */}
        <div>
          <label className="block text-sm font-medium text-white/80 mb-2">
            Current Level
          </label>
          <select
            name="level"
            value={formData.level}
            onChange={handleChange}
            className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
          >
            <option value="Beginner" className="bg-dark-800">Beginner</option>
            <option value="Intermediate" className="bg-dark-800">Intermediate</option>
            <option value="Advanced" className="bg-dark-800">Advanced</option>
          </select>
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-white/80 mb-2">
            Description (Optional)
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="What do you want to achieve with this skill?"
            rows={3}
            className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none transition-all duration-200"
          />
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4">
          <button
            type="button"
            onClick={onCancel}
            className="flex-1 py-3 px-4 bg-white/10 text-white rounded-lg hover:bg-white/20 transition-all duration-200"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <motion.button
            type="submit"
            disabled={isSubmitting}
            className="flex-1 py-3 px-4 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-lg hover:from-primary-600 hover:to-primary-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            whileHover={{ scale: isSubmitting ? 1 : 1.02 }}
            whileTap={{ scale: isSubmitting ? 1 : 0.98 }}
          >
            {isSubmitting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                Adding...
              </>
            ) : (
              <>
                <Plus size={16} />
                Add Skill
              </>
            )}
          </motion.button>
        </div>
      </form>
    </motion.div>
  );
};

export default SkillForm;
