// src/components/SkillCard.jsx
import { motion } from 'framer-motion';
import { 
  Play, 
  Pause, 
  BarChart3, 
  Calendar, 
  Target, 
  Flame,
  Clock,
  Award,
  TrendingUp
} from 'lucide-react';
import { useState } from 'react';

const SkillCard = ({ 
  skill, 
  onStart, 
  onPause, 
  onUpdateProgress, 
  isActive = false,
  userProgress 
}) => {
  const [isHovered, setIsHovered] = useState(false);

  // Get progress percentage
  const progressPercentage = skill.progress || 0;
  
  // Get level color
  const getLevelColor = (level) => {
    switch (level) {
      case 'Beginner': return 'text-green-400 bg-green-400/10';
      case 'Intermediate': return 'text-yellow-400 bg-yellow-400/10';
      case 'Advanced': return 'text-red-400 bg-red-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  // Get category icon and color
  const getCategoryStyle = (category) => {
    const styles = {
      programming: { icon: '💻', color: 'from-primary-500 to-accent-500' },
      languages: { icon: '🌍', color: 'from-secondary-500 to-primary-500' },
      productivity: { icon: '⚡', color: 'from-accent-500 to-secondary-500' },
      creative: { icon: '🎨', color: 'from-primary-400 to-secondary-400' },
      health: { icon: '🏃‍♂️', color: 'from-green-500 to-primary-500' },
      professional: { icon: '💼', color: 'from-secondary-600 to-accent-600' },
    };
    return styles[category?.toLowerCase()] || { icon: '📚', color: 'from-primary-500 to-secondary-500' };
  };

  const categoryStyle = getCategoryStyle(skill.category);

  // Format last studied date
  const formatLastStudied = (date) => {
    if (!date) return 'Never';
    const now = new Date();
    const studiedDate = new Date(date);
    const diffTime = Math.abs(now - studiedDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return 'Today';
    if (diffDays === 2) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays - 1} days ago`;
    return studiedDate.toLocaleDateString();
  };

  return (
    <motion.div
      className="card-glass p-6 group cursor-pointer relative overflow-hidden"
      whileHover={{ y: -5, scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Background Gradient */}
      <div className={`absolute inset-0 bg-gradient-to-br ${categoryStyle.color} opacity-5 group-hover:opacity-10 transition-opacity duration-300`} />
      
      {/* Active Indicator */}
      {isActive && (
        <div className="absolute top-4 right-4">
          <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse" />
        </div>
      )}

      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${categoryStyle.color} flex items-center justify-center text-2xl group-hover:scale-110 transition-transform duration-200`}>
            {categoryStyle.icon}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white group-hover:text-primary-300 transition-colors duration-200">
              {skill.name || skill.subTopic}
            </h3>
            <p className="text-sm text-white/60 capitalize">
              {skill.category}
            </p>
          </div>
        </div>

        {/* Level Badge */}
        <div className={`px-3 py-1 rounded-full text-xs font-medium ${getLevelColor(skill.level)}`}>
          {skill.level}
        </div>
      </div>

      {/* Description */}
      {skill.description && (
        <p className="text-white/70 text-sm mb-4 line-clamp-2">
          {skill.description}
        </p>
      )}

      {/* Progress Bar */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-white/60">Progress</span>
          <span className="text-sm font-medium text-white">{progressPercentage}%</span>
        </div>
        <div className="w-full bg-white/10 rounded-full h-2 overflow-hidden">
          <motion.div
            className={`h-full bg-gradient-to-r ${categoryStyle.color} rounded-full`}
            initial={{ width: 0 }}
            animate={{ width: `${progressPercentage}%` }}
            transition={{ duration: 1, delay: 0.2 }}
          />
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-3 gap-4 mb-4">
        <div className="text-center">
          <div className="flex items-center justify-center gap-1 text-accent-400 mb-1">
            <Flame size={14} />
            <span className="text-sm font-medium">{skill.streak || 0}</span>
          </div>
          <p className="text-xs text-white/50">Streak</p>
        </div>

        <div className="text-center">
          <div className="flex items-center justify-center gap-1 text-primary-400 mb-1">
            <Clock size={14} />
            <span className="text-sm font-medium">
              {formatLastStudied(skill.lastStudiedAt)}
            </span>
          </div>
          <p className="text-xs text-white/50">Last Study</p>
        </div>

        <div className="text-center">
          <div className="flex items-center justify-center gap-1 text-secondary-400 mb-1">
            <Award size={14} />
            <span className="text-sm font-medium">{skill.level}</span>
          </div>
          <p className="text-xs text-white/50">Level</p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-2">
        <motion.button
          className={`flex-1 flex items-center justify-center gap-2 py-2 px-4 rounded-lg font-medium transition-all duration-200 ${
            isActive 
              ? 'bg-red-500/20 text-red-400 hover:bg-red-500/30' 
              : `bg-gradient-to-r ${categoryStyle.color} bg-opacity-20 text-white hover:bg-opacity-30`
          }`}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => isActive ? onPause?.(skill) : onStart?.(skill)}
        >
          {isActive ? <Pause size={16} /> : <Play size={16} />}
          {isActive ? 'Pause' : 'Start'}
        </motion.button>

        <motion.button
          className="p-2 rounded-lg bg-white/10 text-white/70 hover:bg-white/20 hover:text-white transition-all duration-200"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => onUpdateProgress?.(skill)}
        >
          <BarChart3 size={16} />
        </motion.button>
      </div>

      {/* Hover Effect */}
      <motion.div
        className="absolute inset-0 border-2 border-transparent rounded-xl"
        animate={{
          borderColor: isHovered ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
        }}
        transition={{ duration: 0.2 }}
      />
    </motion.div>
  );
};

export default SkillCard;
