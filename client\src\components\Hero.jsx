import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';
import { Sparkles, Zap, Target, Brain } from 'lucide-react';

export default function Hero() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  const floatingIcons = [
    { Icon: Sparkles, delay: 0, x: '10%', y: '20%' },
    { Icon: Zap, delay: 1, x: '85%', y: '15%' },
    { Icon: Target, delay: 2, x: '15%', y: '70%' },
    { Icon: Brain, delay: 1.5, x: '80%', y: '75%' },
  ];

  return (
    <section className="relative min-h-screen flex flex-col items-center justify-center text-center px-6 pt-28 overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Enhanced Dynamic Gradient Orbs */}
        <motion.div
          className="absolute w-[500px] h-[500px] bg-gradient-to-r from-primary-400/25 via-secondary-400/20 to-accent-400/25 rounded-full blur-3xl"
          animate={{
            x: mousePosition.x * 0.03,
            y: mousePosition.y * 0.03,
            scale: [1, 1.1, 1],
          }}
          transition={{
            scale: { duration: 8, repeat: Infinity, ease: "easeInOut" }
          }}
          style={{ top: '5%', left: '5%' }}
        />
        <motion.div
          className="absolute w-[400px] h-[400px] bg-gradient-to-r from-accent-400/20 via-primary-400/25 to-secondary-400/20 rounded-full blur-3xl"
          animate={{
            x: mousePosition.x * -0.02,
            y: mousePosition.y * -0.02,
            scale: [1, 1.2, 1],
          }}
          transition={{
            scale: { duration: 10, repeat: Infinity, ease: "easeInOut" }
          }}
          style={{ bottom: '5%', right: '5%' }}
        />
        <motion.div
          className="absolute w-[300px] h-[300px] bg-gradient-to-r from-secondary-400/15 to-accent-400/20 rounded-full blur-2xl"
          animate={{
            x: mousePosition.x * 0.015,
            y: mousePosition.y * 0.015,
            scale: [1, 1.15, 1],
          }}
          transition={{
            scale: { duration: 12, repeat: Infinity, ease: "easeInOut" }
          }}
          style={{ top: '50%', left: '50%', transform: 'translate(-50%, -50%)' }}
        />

        {/* Enhanced Floating Icons */}
        {floatingIcons.map(({ Icon, delay, x, y }, index) => (
          <motion.div
            key={index}
            className="absolute text-white/30"
            style={{ left: x, top: y }}
            animate={{
              y: [0, -25, 0],
              rotate: [0, 15, -15, 0],
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration: 5 + index * 0.5,
              delay,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          >
            <Icon size={36} />
          </motion.div>
        ))}

        {/* Subtle Grid Pattern */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(251,191,36,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(251,191,36,0.03)_1px,transparent_1px)] bg-[size:60px_60px]" />
      </div>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="relative z-10 max-w-6xl mx-auto"
      >
        {/* Enhanced Badge */}
        <motion.div
          variants={itemVariants}
          className="inline-flex items-center gap-3 glass px-6 py-3 rounded-full text-white/90 text-sm font-medium mb-10 hover:glass-hover transition-all duration-300"
          whileHover={{ scale: 1.05 }}
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
          >
            <Sparkles size={18} className="text-accent-400" />
          </motion.div>
          <span className="font-semibold">AI-Powered Productivity Suite</span>
          <div className="w-2 h-2 bg-success-400 rounded-full animate-pulse-soft" />
        </motion.div>

        {/* Enhanced Main Heading */}
        <motion.h1
          variants={itemVariants}
          className="text-4xl md:text-6xl lg:text-7xl xl:text-8xl font-display font-bold text-white leading-tight mb-8"
        >
          <motion.span
            className="block"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.8 }}
          >
            Stop
          </motion.span>
          <motion.span
            className="text-gradient block bg-gradient-to-r from-primary-400 via-secondary-400 to-accent-400 bg-clip-text text-transparent"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.8 }}
          >
            Procrastinating
          </motion.span>
          <motion.span
            className="block text-3xl md:text-4xl lg:text-5xl xl:text-6xl mt-4 text-white/90 font-medium"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.8 }}
          >
            Start Achieving
          </motion.span>
        </motion.h1>

        {/* Enhanced Subtext */}
        <motion.p
          variants={itemVariants}
          className="text-lg md:text-xl lg:text-2xl text-white/80 max-w-4xl mx-auto mb-14 leading-relaxed font-medium"
        >
          Transform your productivity with
          <span className="text-primary-300 font-semibold"> AI-powered task management</span>,
          <span className="text-secondary-300 font-semibold"> Pomodoro techniques</span>,
          <span className="text-accent-300 font-semibold"> mood tracking</span>, and
          <span className="text-success-300 font-semibold"> personalized skill building</span>
          — all in one beautiful, intuitive platform.
        </motion.p>

        {/* Enhanced CTA Buttons */}
        <motion.div
          variants={itemVariants}
          className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16"
        >
          <motion.div
            whileHover={{ scale: 1.08, y: -2 }}
            whileTap={{ scale: 0.95 }}
            className="relative"
          >
            <Link
              to="/register"
              className="btn-primary text-lg px-10 py-5 relative overflow-hidden group min-w-[220px] font-semibold shadow-button-hover"
            >
              <span className="relative z-10 flex items-center gap-3">
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                >
                  <Zap size={22} />
                </motion.div>
                Get Started Free
              </span>
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-accent-500 via-secondary-500 to-primary-500 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                whileHover={{ scale: 1.05 }}
              />
            </Link>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.08, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link
              to="/login"
              className="btn-glass text-lg px-10 py-5 min-w-[220px] flex items-center gap-3 justify-center font-semibold border-2 border-white/20 hover:border-white/40"
            >
              <Target size={22} />
              Sign In
            </Link>
          </motion.div>
        </motion.div>

        {/* Enhanced Stats */}
        <motion.div
          variants={itemVariants}
          className="grid grid-cols-3 gap-12 max-w-2xl mx-auto"
        >
          {[
            { number: '15K+', label: 'Happy Users', icon: '👥' },
            { number: '100K+', label: 'Tasks Completed', icon: '✅' },
            { number: '98%', label: 'Success Rate', icon: '🎯' },
          ].map((stat, index) => (
            <motion.div
              key={index}
              className="text-center group"
              whileHover={{ scale: 1.05, y: -5 }}
              transition={{ duration: 0.3 }}
            >
              <motion.div
                className="text-3xl mb-2"
                animate={{
                  rotate: [0, 10, -10, 0],
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  delay: index * 0.5,
                  ease: "easeInOut"
                }}
              >
                {stat.icon}
              </motion.div>
              <div className="text-3xl md:text-4xl font-bold text-white mb-2 group-hover:text-primary-300 transition-colors duration-300">
                {stat.number}
              </div>
              <div className="text-sm md:text-base text-white/70 font-medium group-hover:text-white/90 transition-colors duration-300">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>
      </motion.div>
    </section>
  );
}
