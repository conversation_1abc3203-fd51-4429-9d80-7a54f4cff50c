import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';

export default function Navbar() {
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <motion.nav
      className={`fixed top-0 w-full px-6 py-4 flex items-center justify-between z-50 transition-all duration-300 ${
        scrolled
          ? 'glass border-b border-white/10 shadow-glass'
          : 'bg-transparent'
      }`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Enhanced Logo */}
      <motion.div
        whileHover={{ scale: 1.08, y: -2 }}
        whileTap={{ scale: 0.95 }}
        className="relative"
      >
        <Link
          to="/"
          className="flex items-center space-x-3 text-2xl font-display font-bold text-white hover:text-primary-300 transition-all duration-300 group"
        >
          <motion.div
            className="relative"
            animate={{
              rotate: [0, 15, -15, 0],
              scale: [1, 1.1, 1]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              repeatDelay: 2,
              ease: "easeInOut"
            }}
          >
            <span className="text-4xl drop-shadow-lg">🧠</span>
            <motion.div
              className="absolute inset-0 bg-primary-400/20 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            />
          </motion.div>
          <motion.span
            className="text-gradient bg-gradient-to-r from-primary-400 via-secondary-400 to-accent-400 bg-clip-text text-transparent font-black tracking-tight"
            whileHover={{ scale: 1.05 }}
          >
            Procrastinot
          </motion.span>
        </Link>
      </motion.div>

      {/* Enhanced Navigation Links - Hidden on mobile, shown on desktop */}
      <div className="hidden lg:flex items-center space-x-10">
        {[
          { name: 'Features', href: '#features', emoji: '⚡' },
          { name: 'About', href: '#about', emoji: '💡' },
          { name: 'Contact', href: '#contact', emoji: '📧' },
        ].map((link) => (
          <motion.div
            key={link.name}
            whileHover={{ y: -2 }}
            transition={{ duration: 0.2 }}
          >
            <Link
              to={link.href}
              className="group flex items-center gap-2 text-white/80 hover:text-white font-semibold transition-all duration-300 relative"
            >
              <motion.span
                className="text-sm group-hover:scale-110 transition-transform duration-200"
                whileHover={{ rotate: 10 }}
              >
                {link.emoji}
              </motion.span>
              <span className="relative">
                {link.name}
                <motion.div
                  className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary-400 to-secondary-400 group-hover:w-full transition-all duration-300"
                  layoutId="nav-underline"
                />
              </span>
            </Link>
          </motion.div>
        ))}
      </div>

      {/* Enhanced Action Buttons */}
      <div className="flex items-center gap-4">
        <motion.div
          whileHover={{ scale: 1.08, y: -2 }}
          whileTap={{ scale: 0.95 }}
        >
          <Link
            to="/login"
            className="btn-glass text-sm md:text-base px-5 md:px-7 py-2.5 md:py-3.5 font-semibold border-2 border-white/20 hover:border-white/40 transition-all duration-300"
          >
            <span className="flex items-center gap-2">
              <span className="text-xs">👤</span>
              Sign In
            </span>
          </Link>
        </motion.div>

        <motion.div
          whileHover={{ scale: 1.08, y: -2 }}
          whileTap={{ scale: 0.95 }}
          className="relative"
        >
          <Link
            to="/register"
            className="btn-primary text-sm md:text-base px-5 md:px-7 py-2.5 md:py-3.5 relative overflow-hidden group font-semibold shadow-button-hover"
          >
            <span className="relative z-10 flex items-center gap-2">
              <motion.span
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="text-xs"
              >
                ⚡
              </motion.span>
              Get Started
            </span>
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-accent-500 via-secondary-500 to-primary-500 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              whileHover={{ scale: 1.05 }}
            />
          </Link>
        </motion.div>

        {/* Mobile Menu Button */}
        <motion.button
          className="lg:hidden w-10 h-10 glass rounded-full flex items-center justify-center text-white/80 hover:text-white hover:glass-hover transition-all duration-300"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
          aria-label="Menu"
        >
          <motion.div
            animate={{ rotate: [0, 180, 0] }}
            transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          >
            ☰
          </motion.div>
        </motion.button>
      </div>
    </motion.nav>
  );
}
