import { motion } from 'framer-motion';
import { TimerIcon, SmileIcon, BrainIcon, CalendarIcon, RocketIcon, GraduationCapIcon, Sparkles, Zap } from 'lucide-react';

const features = [
  {
    title: 'Pomodoro Timer',
    description: 'Master your focus with scientifically-proven time management techniques, smart break reminders, and productivity insights.',
    icon: <TimerIcon className="w-10 h-10" />,
    gradient: 'from-primary-400 to-secondary-500',
    color: 'text-primary-400',
    bgColor: 'bg-primary-500/20',
    to: '/pomodoro',
    emoji: '⏰'
  },
  {
    title: 'Smart Task Manager',
    description: 'Organize, prioritize, and conquer your tasks with intelligent categorization and deadline tracking.',
    icon: <CalendarIcon className="w-10 h-10" />,
    gradient: 'from-secondary-400 to-accent-500',
    color: 'text-secondary-400',
    bgColor: 'bg-secondary-500/20',
    to: '/tasks',
    emoji: '📋'
  },
  {
    title: 'AI Assistant',
    description: 'Get personalized productivity recommendations and intelligent task breakdowns powered by AI.',
    icon: <BrainIcon className="w-10 h-10" />,
    gradient: 'from-accent-400 to-primary-500',
    color: 'text-accent-400',
    bgColor: 'bg-accent-500/20',
    to: '/ai-tasks',
    emoji: '🤖'
  },
  {
    title: 'Mood Insights',
    description: 'Track your emotional patterns and discover what drives your peak productivity moments.',
    icon: <SmileIcon className="w-10 h-10" />,
    gradient: 'from-success-400 to-primary-500',
    color: 'text-success-400',
    bgColor: 'bg-success-500/20',
    to: '/mood',
    emoji: '😊'
  },
  {
    title: 'Skill Development',
    description: 'Accelerate your growth with curated learning paths, progress tracking, and achievement milestones.',
    icon: <GraduationCapIcon className="w-10 h-10" />,
    gradient: 'from-warning-400 to-secondary-500',
    color: 'text-warning-400',
    bgColor: 'bg-warning-500/20',
    to: '/skills',
    emoji: '🎓'
  },
  {
    title: 'Daily Challenges',
    description: 'Stay motivated with gamified productivity missions, streaks, and rewarding achievement systems.',
    icon: <RocketIcon className="w-10 h-10" />,
    gradient: 'from-error-400 to-accent-500',
    color: 'text-error-400',
    bgColor: 'bg-error-500/20',
    to: '/challenges',
    emoji: '🚀'
  },
];

export default function Features() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.9 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="features" className="relative py-32 px-6 overflow-hidden">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-primary-400/15 via-secondary-400/10 to-accent-400/15 rounded-full blur-3xl animate-float-slow" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-accent-400/15 via-primary-400/10 to-secondary-400/15 rounded-full blur-3xl animate-float" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-success-400/10 to-warning-400/10 rounded-full blur-2xl animate-pulse-soft" />

        {/* Subtle Grid Pattern */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(251,191,36,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(251,191,36,0.02)_1px,transparent_1px)] bg-[size:80px_80px]" />
      </div>

      <div className="relative max-w-7xl mx-auto">
        {/* Enhanced Section Header */}
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="inline-flex items-center gap-3 glass px-6 py-3 rounded-full text-white/90 text-sm font-semibold mb-8 hover:glass-hover transition-all duration-300"
            whileHover={{ scale: 1.05, y: -2 }}
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
            >
              <Sparkles size={18} className="text-accent-400" />
            </motion.div>
            <span>Powerful Features</span>
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            >
              <Zap size={18} className="text-primary-400" />
            </motion.div>
          </motion.div>

          <motion.h2
            className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-display font-bold text-white mb-8 leading-tight"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Everything You Need to
            <motion.span
              className="text-gradient block mt-2 bg-gradient-to-r from-primary-400 via-secondary-400 to-accent-400 bg-clip-text text-transparent"
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
            >
              Succeed & Thrive
            </motion.span>
          </motion.h2>

          <motion.p
            className="text-lg md:text-xl text-white/80 max-w-3xl mx-auto font-medium"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
          >
            Discover a comprehensive suite of productivity tools designed to help you overcome procrastination and achieve your goals with confidence.
          </motion.p>

          <p className="text-lg md:text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
            Discover a comprehensive suite of tools designed to transform your productivity
            and help you achieve your goals faster than ever before.
          </p>
        </motion.div>

        {/* Enhanced Features Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          className="grid gap-8 md:gap-10 md:grid-cols-2 lg:grid-cols-3 mb-16"
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="group relative"
              whileHover={{ y: -8 }}
              transition={{ duration: 0.3 }}
            >
              {/* Enhanced Feature Card */}
              <div className="card-glass p-8 h-full min-h-[380px] flex flex-col relative overflow-hidden hover:shadow-glass-hover transition-all duration-500">
                {/* Dynamic Background Gradient */}
                <motion.div
                  className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-8 transition-opacity duration-500 rounded-2xl`}
                  whileHover={{ opacity: 0.08 }}
                />

                {/* Emoji Badge */}
                <motion.div
                  className="absolute -top-3 -right-3 w-12 h-12 bg-gradient-to-br from-white/20 to-white/5 backdrop-blur-sm rounded-full flex items-center justify-center text-2xl border border-white/10 group-hover:border-white/30 transition-all duration-300"
                  whileHover={{ scale: 1.1, rotate: 10 }}
                  animate={{
                    y: [0, -5, 0],
                    rotate: [0, 5, -5, 0]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    delay: index * 0.5,
                    ease: "easeInOut"
                  }}
                >
                  {feature.emoji}
                </motion.div>

                {/* Enhanced Icon Container */}
                <motion.div
                  className="mb-8"
                  whileHover={{ scale: 1.15, rotate: 8 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className={`w-20 h-20 rounded-3xl bg-gradient-to-br ${feature.gradient} bg-opacity-15 backdrop-blur-sm flex items-center justify-center border border-white/10 group-hover:border-white/30 transition-all duration-300 shadow-lg`}>
                    <motion.div
                      className={`${feature.color} group-hover:scale-110 transition-transform duration-300`}
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.6 }}
                    >
                      {feature.icon}
                    </motion.div>
                  </div>

                  {/* Glow Effect */}
                  <motion.div
                    className={`absolute inset-0 rounded-3xl bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-500`}
                    initial={{ opacity: 0 }}
                    whileHover={{ opacity: 0.2 }}
                  />
                </motion.div>

                {/* Enhanced Content */}
                <div className="flex-1 flex flex-col">
                  <motion.h3
                    className="text-2xl font-display font-bold text-white mb-4 group-hover:text-primary-200 transition-colors duration-300"
                    whileHover={{ x: 5 }}
                  >
                    {feature.title}
                  </motion.h3>

                  <motion.p
                    className="text-white/75 text-base leading-relaxed flex-1 group-hover:text-white/90 transition-colors duration-300 font-medium"
                    whileHover={{ x: 3 }}
                  >
                    {feature.description}
                  </motion.p>

                  {/* Enhanced Action Area */}
                  <motion.div
                    className="mt-8 pt-6 border-t border-white/10 group-hover:border-white/20 transition-colors duration-300"
                    whileHover={{ y: -2 }}
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-xs font-bold text-white/60 uppercase tracking-wider group-hover:text-white/80 transition-colors duration-300">
                        Explore Feature
                      </span>
                      <motion.div
                        className={`flex items-center gap-2 ${feature.color} group-hover:text-white transition-colors duration-300 font-semibold`}
                        whileHover={{ x: 8 }}
                      >
                        <span className="text-sm">Launch</span>
                        <motion.div
                          animate={{ x: [0, 3, 0] }}
                          transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                        >
                          →
                        </motion.div>
                      </motion.div>
                    </div>
                  </motion.div>
                </div>

                {/* Enhanced Hover Border Effect */}
                <motion.div
                  className="absolute inset-0 rounded-2xl border-2 border-transparent group-hover:border-white/20 transition-colors duration-500"
                  whileHover={{ opacity: 1 }}
                />
              </div>

              {/* Enhanced Floating Elements */}
              <motion.div
                className={`absolute -top-2 -left-2 w-5 h-5 bg-gradient-to-br ${feature.gradient} rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300`}
                animate={{
                  rotate: 360,
                  scale: [1, 1.2, 1]
                }}
                transition={{
                  rotate: { duration: 8, repeat: Infinity, ease: "linear" },
                  scale: { duration: 2, repeat: Infinity, ease: "easeInOut" }
                }}
              />
              <motion.div
                className={`absolute -bottom-2 -right-2 w-4 h-4 bg-gradient-to-br ${feature.gradient} rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300`}
                animate={{
                  rotate: -360,
                  scale: [1, 1.3, 1]
                }}
                transition={{
                  rotate: { duration: 6, repeat: Infinity, ease: "linear" },
                  scale: { duration: 2.5, repeat: Infinity, ease: "easeInOut", delay: 0.5 }
                }}
              />
            </motion.div>
          ))}
        </motion.div>

        {/* Enhanced Bottom CTA */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="glass-card p-12 max-w-2xl mx-auto"
            whileHover={{ scale: 1.02, y: -5 }}
            transition={{ duration: 0.3 }}
          >
            <motion.div
              className="text-4xl mb-6"
              animate={{
                rotate: [0, 10, -10, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              🚀
            </motion.div>

            <motion.h3
              className="text-2xl md:text-3xl font-display font-bold text-white mb-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              Ready to Transform Your Productivity?
            </motion.h3>

            <motion.p
              className="text-white/80 text-lg mb-8 font-medium"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              Join thousands of users who've already conquered procrastination and achieved their goals.
            </motion.p>

            <motion.button
              className="btn-primary text-lg px-10 py-5 font-semibold shadow-button-hover"
              whileHover={{ scale: 1.08, y: -3 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => window.location.href = "/register"}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <span className="flex items-center gap-3">
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                >
                  ⚡
                </motion.div>
                Start Your Journey Today
              </span>
            </motion.button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
