// src/components/SkillFilters.jsx
import { motion } from 'framer-motion';
import { Search, Filter, X, ChevronDown } from 'lucide-react';
import { useState } from 'react';

const SkillFilters = ({ filters, setFilters, categories, stats }) => {
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      category: 'all',
      level: 'all',
      progress: 'all',
      search: '',
    });
  };

  const hasActiveFilters = filters.category !== 'all' || 
                          filters.level !== 'all' || 
                          filters.progress !== 'all' || 
                          filters.search !== '';

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white/40" size={20} />
        <input
          type="text"
          placeholder="Search skills..."
          value={filters.search}
          onChange={(e) => handleFilterChange('search', e.target.value)}
          className="w-full pl-12 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
        />
        {filters.search && (
          <button
            onClick={() => handleFilterChange('search', '')}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/40 hover:text-white transition-colors duration-200"
          >
            <X size={16} />
          </button>
        )}
      </div>

      {/* Filter Controls */}
      <div className="flex flex-wrap gap-4 items-center">
        {/* Category Filter */}
        <div className="relative">
          <select
            value={filters.category}
            onChange={(e) => handleFilterChange('category', e.target.value)}
            className="appearance-none bg-white/10 border border-white/20 rounded-lg px-4 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 cursor-pointer"
          >
            <option value="all" className="bg-dark-800 text-white">All Categories</option>
            {categories.map(category => (
              <option key={category.id} value={category.id} className="bg-dark-800 text-white">
                {category.icon} {category.name}
              </option>
            ))}
          </select>
          <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 text-white/40 pointer-events-none" size={16} />
        </div>

        {/* Level Filter */}
        <div className="relative">
          <select
            value={filters.level}
            onChange={(e) => handleFilterChange('level', e.target.value)}
            className="appearance-none bg-white/10 border border-white/20 rounded-lg px-4 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 cursor-pointer"
          >
            <option value="all" className="bg-dark-800 text-white">All Levels</option>
            <option value="Beginner" className="bg-dark-800 text-white">Beginner</option>
            <option value="Intermediate" className="bg-dark-800 text-white">Intermediate</option>
            <option value="Advanced" className="bg-dark-800 text-white">Advanced</option>
          </select>
          <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 text-white/40 pointer-events-none" size={16} />
        </div>

        {/* Progress Filter */}
        <div className="relative">
          <select
            value={filters.progress}
            onChange={(e) => handleFilterChange('progress', e.target.value)}
            className="appearance-none bg-white/10 border border-white/20 rounded-lg px-4 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 cursor-pointer"
          >
            <option value="all" className="bg-dark-800 text-white">All Progress</option>
            <option value="not-started" className="bg-dark-800 text-white">Not Started</option>
            <option value="in-progress" className="bg-dark-800 text-white">In Progress</option>
            <option value="completed" className="bg-dark-800 text-white">Completed</option>
          </select>
          <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 text-white/40 pointer-events-none" size={16} />
        </div>

        {/* Clear Filters */}
        {hasActiveFilters && (
          <motion.button
            onClick={clearFilters}
            className="flex items-center gap-2 px-4 py-2 bg-red-500/20 text-red-400 rounded-lg hover:bg-red-500/30 transition-all duration-200 text-sm"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
          >
            <X size={14} />
            Clear Filters
          </motion.button>
        )}
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <motion.div
          className="glass p-4 rounded-lg text-center"
          whileHover={{ scale: 1.02 }}
        >
          <div className="text-2xl font-bold text-primary-400 mb-1">
            {stats.total}
          </div>
          <div className="text-sm text-white/60">Total Skills</div>
        </motion.div>

        <motion.div
          className="glass p-4 rounded-lg text-center"
          whileHover={{ scale: 1.02 }}
        >
          <div className="text-2xl font-bold text-green-400 mb-1">
            {stats.completed}
          </div>
          <div className="text-sm text-white/60">Completed</div>
        </motion.div>

        <motion.div
          className="glass p-4 rounded-lg text-center"
          whileHover={{ scale: 1.02 }}
        >
          <div className="text-2xl font-bold text-yellow-400 mb-1">
            {stats.inProgress}
          </div>
          <div className="text-sm text-white/60">In Progress</div>
        </motion.div>

        <motion.div
          className="glass p-4 rounded-lg text-center"
          whileHover={{ scale: 1.02 }}
        >
          <div className="text-2xl font-bold text-orange-400 mb-1">
            {stats.longestStreak}
          </div>
          <div className="text-sm text-white/60">Best Streak</div>
        </motion.div>
      </div>
    </div>
  );
};

export default SkillFilters;
